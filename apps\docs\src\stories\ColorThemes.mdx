import { Meta, ColorPalette, ColorItem } from '@storybook/addon-docs/blocks';




<Meta title="Colors" />

<ColorPalette>
  <ColorItem
    title="Light Mode Semantic"
    subtitle="Light mode semantic colors"
    colors={{
      'Background': 'hsl(0, 0%, 100%)',
      'Foreground': 'hsl(0, 0%, 0%)',
      'Card': 'hsl(0, 0%, 100%)',
      'Card Foreground': 'hsl(0, 0%, 0%)',
      'Popover': 'hsl(0, 0%, 100%)',
      'Popover Foreground': 'hsl(0, 0%, 0%)',
      'Primary': 'hsl(191, 50%, 37%)',
      'Primary Foreground': 'hsl(52, 87%, 94%)',
      'Secondary': 'hsl(0, 0%, 94%)',
      'Secondary Foreground': 'hsl(0, 0%, 0%)',
      'Muted': 'hsl(0, 0%, 94%)',
      'Muted Foreground': 'hsl(0, 0%, 0%)',
      'Accent': 'hsl(212, 99%, 69%)',
      'Accent Foreground': 'hsl(210, 99%, 34%)',
      'Destructive': 'hsl(348, 99%, 45%)',
      'Destructive Foreground': 'hsl(0, 0%, 100%)',
      'Border': 'hsl(0, 0%, 75%)',
      'Input': 'hsl(0, 0%, 75%)',
      'Ring': 'hsl(212, 99%, 46%)',
      'Sidebar': 'hsl(0, 0%, 100%)',
      'Sidebar Foreground': 'hsl(0, 0%, 0%)',
      'Sidebar Primary': 'hsl(191, 50%, 37%)',
      'Sidebar Primary Foreground': 'hsl(52, 87%, 94%)',
      'Sidebar Accent': 'hsl(0, 0%, 94%)',
      'Sidebar Accent Foreground': 'hsl(0, 0%, 0%)',
      'Sidebar Border': 'hsl(0, 0%, 75%)',
      'Sidebar Ring': 'hsl(212, 99%, 46%)',
    }}
  />
  <ColorItem
    title="1177 Sky"
    subtitle="Sky color scale"
    colors={{
      'Sky 50': 'hsl(240, 14%, 95%)',
      'Sky 200': 'hsl(210, 33%, 83%)',
      'Sky 400': 'hsl(199, 100%, 45%)',
      'Sky 600': 'hsl(214, 44%, 40%)',
      'Sky 900': 'hsl(230, 26%, 32%)',
    }}
  />
</ColorPalette>
